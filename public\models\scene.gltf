{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 120, "max": [1.0729658603668213, 0.9541289806365967, 2.3967201709747314], "min": [-1.0729658603668213, -1.0290712118148804, 0.2507883310317993], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1440, "componentType": 5126, "count": 120, "max": [0.9980012774467468, 1.0, 0.998001217842102], "min": [-0.9980012774467468, -0.958128035068512, -0.9980013370513916], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 120, "max": [0.9921959042549133, 0.9692263007164001], "min": [0.00048828125, 0.0004882860812358558], "type": "VEC2"}, {"bufferView": 0, "componentType": 5125, "count": 258, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2880, "componentType": 5126, "count": 848, "max": [1.4385813474655151, 0.5612714886665344, 1.8629909753799438], "min": [-1.4385813474655151, -0.5612717270851135, 0.733109712600708], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 13056, "componentType": 5126, "count": 848, "max": [0.998691737651825, 1.0, 0.9997329115867615], "min": [-0.998691737651825, -1.0, -0.9997328519821167], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 960, "componentType": 5126, "count": 848, "max": [0.4630151093006134, 0.7657250761985779], "min": [0.0004882905050180852, 0.040932539850473404], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1032, "componentType": 5125, "count": 2484, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 23232, "componentType": 5126, "count": 272, "max": [1.5136240720748901, 0.69874507188797, 1.246260643005371], "min": [-0.0531386137008667, -0.6987453699111938, 0.6427996158599854], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 26496, "componentType": 5126, "count": 272, "max": [1.0, 0.9925840497016907, 3.474758969446157e-08], "min": [-1.0, -0.992584228515625, -0.9972949028015137], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 7744, "componentType": 5126, "count": 272, "max": [0.49542585015296936, 0.4245557188987732], "min": [0.0883367583155632, 0.04369831457734108], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 10968, "componentType": 5125, "count": 948, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 29760, "componentType": 5126, "count": 120, "max": [0.11631059646606445, 1.423261284828186, 1.066680669784546], "min": [-2.339221477508545, -0.30715319514274597, -0.34619539976119995], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 31200, "componentType": 5126, "count": 120, "max": [1.0, 0.9858053922653198, 0.8051257729530334], "min": [-1.0, -0.41280055046081543, -0.9879500865936279], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 9920, "componentType": 5126, "count": 120, "max": [0.9695915579795837, 0.9465492367744446], "min": [0.0004912933800369501, 0.000607838446740061], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 14760, "componentType": 5125, "count": 204, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 32640, "componentType": 5126, "count": 600, "max": [0.3550208508968353, 0.8540341854095459, 0.421561062335968], "min": [-2.542382001876831, -0.4509912133216858, -0.40002137422561646], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 39840, "componentType": 5126, "count": 600, "max": [0.9944252371788025, 0.9551367163658142, 0.9832706451416016], "min": [-0.9944252371788025, -0.9985772371292114, -0.9606308341026306], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 10880, "componentType": 5126, "count": 600, "max": [0.99951171875, 0.9694443941116333], "min": [0.0005970009369775653, 0.0005933830398134887], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 15576, "componentType": 5125, "count": 1608, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 47040, "componentType": 5126, "count": 100, "max": [0.11677949130535126, 0.21496060490608215, 0.4275549054145813], "min": [-2.3387930393218994, -0.21496060490608215, -0.4275549054145813], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 48240, "componentType": 5126, "count": 100, "max": [0.9991404414176941, 1.0, 1.0], "min": [-0.9991404414176941, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 15680, "componentType": 5126, "count": 100, "max": [0.31622201204299927, 0.9459177255630493], "min": [0.18872886896133423, 0.1354495733976364], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 22008, "componentType": 5125, "count": 180, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 49440, "componentType": 5126, "count": 864, "max": [0.4572032690048218, 0.48369795083999634, 0.6623291969299316], "min": [-2.1547603607177734, -0.6093291640281677, -0.5075265169143677], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 59808, "componentType": 5126, "count": 864, "max": [0.9870508313179016, 0.9963530898094177, 0.9697258472442627], "min": [-0.9870511293411255, -0.9934471249580383, -0.9893192052841187], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 16480, "componentType": 5126, "count": 864, "max": [0.8970814943313599, 0.9275388717651367], "min": [0.03397821635007858, 0.038722582161426544], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 22728, "componentType": 5125, "count": 2484, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 70176, "componentType": 5126, "count": 2659, "max": [1.7290713787078857, 0.06899160891771317, 0.48985886573791504], "min": [-0.06371816247701645, -0.8220371007919312, -0.45660215616226196], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 102084, "componentType": 5126, "count": 2659, "max": [0.9954511523246765, 0.9954047203063965, 0.9908477067947388], "min": [-0.9954515099525452, -0.9953305125236511, -0.9911594390869141], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 23392, "componentType": 5126, "count": 2659, "max": [0.5495383143424988, 0.31074637174606323], "min": [0.3040646016597748, 0.10978756099939346], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 32664, "componentType": 5125, "count": 13824, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 133992, "componentType": 5126, "count": 2669, "max": [1.612809181213379, -0.08182692527770996, 0.5277003049850464], "min": [-0.05861858278512955, -0.8820682764053345, -0.21142058074474335], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 166020, "componentType": 5126, "count": 2669, "max": [0.9997398257255554, 0.9953438639640808, 0.9991823434829712], "min": [-0.9997398257255554, -0.9955102205276489, -0.9991025924682617], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 44664, "componentType": 5126, "count": 2669, "max": [0.4365612268447876, 0.28206774592399597], "min": [0.33942803740501404, 0.0004883113433606923], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 87960, "componentType": 5125, "count": 13824, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 198048, "componentType": 5126, "count": 484, "max": [0.9204850792884827, -0.09375643730163574, 0.9204850792884827], "min": [-0.9204850792884827, -0.30153927206993103, -0.9204850792884827], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 203856, "componentType": 5126, "count": 484, "max": [0.25074878334999084, -0.9680472612380981, 0.2507486641407013], "min": [-0.25074872374534607, -0.9999094605445862, -0.250748872756958], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 66016, "componentType": 5126, "count": 484, "max": [0.9864600896835327, 0.5950649380683899], "min": [0.9033501148223877, 0.5119490623474121], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 143256, "componentType": 5125, "count": 2646, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 209664, "componentType": 5126, "count": 588, "max": [0.20717178285121918, 0.23235948383808136, 0.198886439204216], "min": [-1.0144546031951904, -0.2224201112985611, -0.1875469982624054], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 216720, "componentType": 5126, "count": 588, "max": [0.999024510383606, 0.9725940823554993, 0.2715897262096405], "min": [-0.9990244507789612, -0.9725938439369202, -0.9882991909980774], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 69888, "componentType": 5126, "count": 588, "max": [0.33090654015541077, 0.8093421459197998], "min": [0.00048828125, 0.10661786049604416], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 153840, "componentType": 5125, "count": 1716, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 223776, "componentType": 5126, "count": 236, "max": [1.036696434020996, 1.4088027477264404, 0.20011651515960693], "min": [-0.0657438412308693, -0.12277963757514954, -0.12066972255706787], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 226608, "componentType": 5126, "count": 236, "max": [1.0, 0.9999898672103882, 0.9999927878379822], "min": [-1.0, -0.9999898672103882, -0.9999898672103882], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 74592, "componentType": 5126, "count": 236, "max": [0.17801548540592194, 0.9184978008270264], "min": [0.00048828125, 0.043614450842142105], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 160704, "componentType": 5125, "count": 672, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 229440, "componentType": 5126, "count": 130, "max": [0.09792820364236832, 0.6136369109153748, 0.8039855360984802], "min": [-0.09792802482843399, -0.6136367321014404, -0.803985595703125], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 231000, "componentType": 5126, "count": 130, "max": [0.992298424243927, 0.7985982894897461, 0.7946684956550598], "min": [-0.9922983646392822, -0.7985982894897461, -0.7946701645851135], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 76480, "componentType": 5126, "count": 130, "max": [0.22423534095287323, 0.07942497730255127], "min": [0.00048828125, 0.005268137436360121], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 163392, "componentType": 5125, "count": 372, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 232560, "componentType": 5126, "count": 130, "max": [0.1139368787407875, 0.7717236280441284, 1.0157934427261353], "min": [-0.11393677443265915, -0.7717235684394836, -1.0157935619354248], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 234120, "componentType": 5126, "count": 130, "max": [0.9922982454299927, 0.7985982298851013, 0.7946727275848389], "min": [-0.9922982454299927, -0.79859858751297, -0.794672966003418], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 77520, "componentType": 5126, "count": 130, "max": [0.18684129416942596, 0.19546864926815033], "min": [0.00048828125, 0.00048828125], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 164880, "componentType": 5125, "count": 372, "type": "SCALAR"}], "asset": {"extras": {"author": "<PERSON>_<PERSON> (https://sketchfab.com/<PERSON>_<PERSON>)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/3d-models/flying-robot-07-07-b31dff5e3b344aa1a6f2a3c8454ad440", "title": "Flying Robot 07 / Робот 07"}, "generator": "Sketchfab-16.62.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 166368, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 78560, "byteOffset": 166368, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 235680, "byteOffset": 244928, "byteStride": 12, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 480608, "uri": "scene.bin"}], "extensionsUsed": ["KHR_materials_specular"], "images": [{"uri": "textures/Material.006_baseColor.png"}, {"uri": "textures/Material.006_emissive.png"}], "materials": [{"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 1}, "extensions": {"KHR_materials_specular": {"specularColorFactor": [1.0, 1.0, 1.0], "specularFactor": 0.0}}, "name": "Material.006", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.16914337262742132, "roughnessFactor": 0.4921919762041179}}], "meshes": [{"name": "Cube.004_Material.006_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2}, "indices": 3, "material": 0, "mode": 4}]}, {"name": "Cylinder.007_Material.006_0", "primitives": [{"attributes": {"NORMAL": 5, "POSITION": 4, "TEXCOORD_0": 6}, "indices": 7, "material": 0, "mode": 4}]}, {"name": "Cylinder.008_Material.006_0", "primitives": [{"attributes": {"NORMAL": 9, "POSITION": 8, "TEXCOORD_0": 10}, "indices": 11, "material": 0, "mode": 4}]}, {"name": "Cube.005_Material.006_0", "primitives": [{"attributes": {"NORMAL": 13, "POSITION": 12, "TEXCOORD_0": 14}, "indices": 15, "material": 0, "mode": 4}]}, {"name": "Cylinder.009_Material.006_0", "primitives": [{"attributes": {"NORMAL": 17, "POSITION": 16, "TEXCOORD_0": 18}, "indices": 19, "material": 0, "mode": 4}]}, {"name": "Cube.006_Material.006_0", "primitives": [{"attributes": {"NORMAL": 21, "POSITION": 20, "TEXCOORD_0": 22}, "indices": 23, "material": 0, "mode": 4}]}, {"name": "Cylinder.010_Material.006_0", "primitives": [{"attributes": {"NORMAL": 25, "POSITION": 24, "TEXCOORD_0": 26}, "indices": 27, "material": 0, "mode": 4}]}, {"name": "BezierCurve.002_Material.006_0", "primitives": [{"attributes": {"NORMAL": 29, "POSITION": 28, "TEXCOORD_0": 30}, "indices": 31, "material": 0, "mode": 4}]}, {"name": "BezierCurve.003_Material.006_0", "primitives": [{"attributes": {"NORMAL": 33, "POSITION": 32, "TEXCOORD_0": 34}, "indices": 35, "material": 0, "mode": 4}]}, {"name": "Plane.001_Material.006_0", "primitives": [{"attributes": {"NORMAL": 37, "POSITION": 36, "TEXCOORD_0": 38}, "indices": 39, "material": 0, "mode": 4}]}, {"name": "Cylinder.011_Material.006_0", "primitives": [{"attributes": {"NORMAL": 41, "POSITION": 40, "TEXCOORD_0": 42}, "indices": 43, "material": 0, "mode": 4}]}, {"name": "Cube.007_Material.006_0", "primitives": [{"attributes": {"NORMAL": 45, "POSITION": 44, "TEXCOORD_0": 46}, "indices": 47, "material": 0, "mode": 4}]}, {"name": "Cylinder.012_Material.006_0", "primitives": [{"attributes": {"NORMAL": 49, "POSITION": 48, "TEXCOORD_0": 50}, "indices": 51, "material": 0, "mode": 4}]}, {"name": "Cylinder.013_Material.006_0", "primitives": [{"attributes": {"NORMAL": 53, "POSITION": 52, "TEXCOORD_0": 54}, "indices": 55, "material": 0, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2], "matrix": [0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.0, 0.009999999776482582, 0.0, 0.0, -0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "RB2.fbx"}, {"children": [3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29], "name": "RootNode"}, {"children": [4], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.004"}, {"mesh": 0, "name": "Cube.004_Material.006_0"}, {"children": [6], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 0.0, 2.9577529430389404, -17.3239803314209, 1.0], "name": "Cylinder.007"}, {"mesh": 1, "name": "Cylinder.007_Material.006_0"}, {"children": [8], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -73.02427673339844, -74.3885269165039, 5.053240776062012, 1.0], "name": "Cylinder.008"}, {"mesh": 2, "name": "Cylinder.008_Material.006_0"}, {"children": [10], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 111.14554595947266, 152.46604919433594, -55.65839385986328, 1.0], "name": "Cube.005"}, {"mesh": 3, "name": "Cube.005_Material.006_0"}, {"children": [12], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 109.3680648803711, 35.2091064453125, 25.019777297973633, 1.0], "name": "Cylinder.009"}, {"mesh": 4, "name": "Cylinder.009_Material.006_0"}, {"children": [14], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 111.10067749023438, 105.74415588378906, 68.67344665527344, 1.0], "name": "Cube.006"}, {"mesh": 5, "name": "Cube.006_Material.006_0"}, {"children": [16], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 84.87785339355469, 19.387527465820312, -94.0562744140625, 1.0], "name": "Cylinder.010"}, {"mesh": 6, "name": "Cylinder.010_Material.006_0"}, {"children": [18], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -83.26764678955078, -6.430768966674805, -36.63667297363281, 1.0], "name": "BezierCurve.002"}, {"mesh": 7, "name": "BezierCurve.002_Material.006_0"}, {"children": [20], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -77.70951843261719, -19.48586654663086, -36.63667297363281, 1.0], "name": "BezierCurve.003"}, {"mesh": 8, "name": "BezierCurve.003_Material.006_0"}, {"children": [22], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -1.8339891312280088e-06, 132.3754119873047, 76.96349334716797, 1.0], "name": "Plane.001"}, {"mesh": 9, "name": "Plane.001_Material.006_0"}, {"children": [24], "matrix": [100.0, 0.0, 0.0, 0.0, 0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, 40.36414337158203, -9.026050567626953, 24.226102828979492, 1.0], "name": "Cylinder.011"}, {"mesh": 10, "name": "Cylinder.011_Material.006_0"}, {"children": [26], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -48.547630310058594, 4.550742149353027, 64.30115509033203, 1.0], "name": "Cube.007"}, {"mesh": 11, "name": "Cube.007_Material.006_0"}, {"children": [28], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -40.43001937866211, 281.1916198730469, -171.7247314453125, 1.0], "name": "Cylinder.012"}, {"mesh": 12, "name": "Cylinder.012_Material.006_0"}, {"children": [30], "matrix": [100.0, 0.0, 0.0, 0.0, -0.0, -1.629206793918314e-05, -99.99999999999868, 0.0, 0.0, 99.99999999999868, -1.629206793918314e-05, 0.0, -6.077994346618652, 230.18870544433594, -131.2882080078125, 1.0], "name": "Cylinder.013"}, {"mesh": 13, "name": "Cylinder.013_Material.006_0"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 1}]}