:root {
    /* Theme Colors */
    --primary-color: #000000;
    --secondary-color: #cda5fa;
    --accent-color: #9ae99e;
    
    /* Typography */
    --ITC-font: "ITC";

    /* Easing  */
    --ease: cubic-bezier(0.66, 0, 0.34, 1);

    /* Spacings */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    --space-2xl: 4rem;
    --space-3xl: 6rem;
}

@font-face {
  font-family: NeueMontreal;
  src: url(fonts/NeueMontreal-Regular.otf);
  font-display: swap; /* Performance: Show fallback font while loading */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}



body {
  background-color: var(--primary-color);
  color: var(--fg);
  width: 100vw;
  height: 100svh;
  overflow: hidden;
}

#main{
  width: 100%;
  overflow-x: hidden;
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
a,
button,
input,
textarea,
label,
select,
option {
  font-family: NeueMontreal;
}

.inversion-lens {
  position: relative;
  width: 100vw;
  height: 100svh;
  overflow: hidden;
}

.inversion-lens img {
  display: none;
}


 /* split text  */  
.lineParent {
    overflow: hidden;
    max-width: max-content;
    height: auto;
}

/* button */
.btn-cta {
    position: relative;
    display: inline-block;
    padding: 0.8rem 1.5rem;
    margin-top: 1rem;
    border-radius: 1000px;
    font-weight: 100;
    font-size:1.3em;
    line-height: 110%;
    text-transform: uppercase;
    transition: transform .3s;
    overflow: hidden;
    color: var(--secondary-color);
    text-decoration: none;
  }
  
  
  .btn-cta:hover {
    transform: scaleX(1.02);
    transition: transform .6s cubic-bezier(.34, 5.56, .64, 1);
  }
  
  
  .btn-cta-border {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 1px;
    z-index: 3;
    border: 1px solid;
    border-radius: 1000px;
  }
  
  .btn-cta-ripple {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    transform: translateZ(0);
    border-radius: inherit;
  }
  
  .btn-cta-ripple span {
    display: block;
    width: 100%;
    height: 100%;
    transform: translateY(101%);
    background: currentColor;
    border-radius: 50% 50% 0 0;
    transition: transform .5s cubic-bezier(.4, 0, 0, 1), border-radius .5s cubic-bezier(.4, 0, 0, 1);
  }
  
  .btn-cta:hover .btn-cta-ripple span {
    border-radius: 0;
    transform: translateY(0);
    transition-duration: .5s, .9s;
  }
  
  .btn-cta-title {
    position: relative;
    display: block;
    padding: 0 .16em 0 0;
    overflow: hidden;
    z-index: 2;
  }
  
  .btn-cta-title span {
    display: block;
    transition: transform .8s cubic-bezier(.16, 1, .3, 1);
  }
  
  .btn-cta-title span:after {
    content: attr(data-text);
    display: block;
    position: absolute;
    top: 110%;
    left: 0;
    color: var(--primary-color);
  }
  
  .btn-cta-title span::selection{
    background: var(--secondary-color);
    color: var(--primary-color);
  }
  
  .btn-cta:hover .btn-cta-title span {
    transform: translateY(-110%);
  }
  


     .frame {
        z-index: 997;
        pointer-events: none;
        min-width: 100%;
        min-height: 100vh;
        position: fixed;
        inset: 0%;
      }

      .frame_box {
        z-index: 1;
        pointer-events: none;
        width: 100%;
        height: 100%;
        display: block;
        position: relative;
      }

    
      .frame_border-top {
        z-index: 9;
        background-color: #070707;
        width: 100%;
        height: 1.4vw; 
               position: absolute;
        inset: 0% 0% auto;
      }

      .frame_border-bottom {
        z-index: 9;
        background-color: #070707;
        width: 100%;
        height: 1vw;        position: absolute;
        inset: auto 0% 0%;
      }

      .frame_border-left {
        z-index: 9;
        background-color: #070707;
        justify-content: flex-start;
        align-items: stretch;
           width: 1vw;
        height: 100%;
        display: block;
        position: absolute;
        inset: 0% auto 0% 0%;
      }

      .frame_border-right {
        z-index: 9;
        background-color: #070707;
                 width: 1vw;
       height: 100%;
        display: block;
        position: absolute;
        inset: 0% 0% 0% auto;
      }

  
      
/* FOOTER */

.footer-section{
    width: 100%;
    height: 100vh;
    background: var(--secondary-color);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    gap: 1rem;
    z-index: 100;
}

.footer-text-wrapper{
  flex: 1;
  overflow: hidden;
  width: 100%;
  height: 100%;
    padding: var(--space-lg);

}

.footer-text-wrapper p{
  font-size: clamp(1.5rem,1.5vw,1.5rem);
    line-height: 120%;

    font-weight: 100;
    max-width: 50%;
}



.footer-text-wrapper p::selection{
  background: var(--primary-color);
  color: var(--secondary-color);
}

.footer-bottom{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-logo h1{
  font-size: clamp(2.5rem,4.5vw,4.5rem);
    line-height: 120%;
}

.footer-dets {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.footer-dets a {
  font-size: clamp(1.5rem, 1.5vw, 1.5rem);
  line-height: 120%;
  color: var(--primary-color);
  text-decoration: none;
  position: relative;
  display: inline-block;
}

.footer-dets a::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -2px; /* Adjust distance from text */
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.footer-dets a:hover::after {
  transform: scaleX(1);
}

.footer-dets a::selection{
  background: var(--primary-color);
  color: var(--secondary-color);
}



/* button */
.btn-cta-footer {
    position: relative;
    display: inline-block;
    padding: 0.8rem 1.5rem;
    margin-top: 1rem;
    border-radius: 1000px;
    font-weight: 100;
    font-size:1.3em;
    line-height: 110%;
    text-transform: uppercase;
    transition: transform .3s;
    overflow: hidden;
    color: var(--primary-color);
    text-decoration: none;
  }
  
  
  .btn-cta-footer:hover {
    transform: scaleX(1.02);
    transition: transform .6s cubic-bezier(.34, 5.56, .64, 1);
  }
  
  
  .btn-cta-border-footer {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 1px;
    z-index: 3;
    border: 1px solid;
    border-radius: 1000px;
  }
  
  .btn-cta-ripple-footer {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    transform: translateZ(0);
    border-radius: inherit;
  }
  
  .btn-cta-ripple-footer span {
    display: block;
    width: 100%;
    height: 100%;
    transform: translateY(101%);
    background: currentColor;
    border-radius: 50% 50% 0 0;
    transition: transform .5s cubic-bezier(.4, 0, 0, 1), border-radius .5s cubic-bezier(.4, 0, 0, 1);
  }
  
  .btn-cta-footer:hover .btn-cta-ripple-footer span {
    border-radius: 0;
    transform: translateY(0);
    transition-duration: .5s, .9s;
  }
  
  .btn-cta-title-footer {
    position: relative;
    display: block;
    padding: 0 .16em 0 0;
    overflow: hidden;
    z-index: 2;
  }
  
  .btn-cta-title-footer span {
    display: block;
    transition: transform .8s cubic-bezier(.16, 1, .3, 1);
  }
  
  .btn-cta-title-footer span:after {
    content: attr(data-text);
    display: block;
    position: absolute;
    top: 110%;
    left: 0;
    color: var(--secondary-color);
  }
  
  .btn-cta-title-footer span::selection{
    background: var(--secondary-color);
    color: var(--primary-color);
  }
  
  .btn-cta-footer:hover .btn-cta-title-footer span {
    transform: translateY(-110%);
  }
  