<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<style>

  body{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    overflow-x: hidden;
    background: #000;
  }


  .narbar{
    position: fixed;
    width: 100vw;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 10rem;
    z-index: 100;
  }

  .logo{
    width: 40%;
    height: 1rem;
    background: blue;
  }

  .menu-btn{
    width: 30%;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: right;
    background: rgb(0, 255, 115);
  }

  .menu-overlay{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;}



/* button */
.btn-cta-navbar {
    position: relative;
    display: inline-block;
    padding: 0.5rem 1.5rem;
    margin-top: 1rem;
    border-radius: 1000px;
    font-weight: 100;
    font-size:1.3em;
    line-height: 110%;
    text-transform: uppercase;
    transition: transform .3s;
    overflow: hidden;
    color: wheat;
    text-decoration: none;
  }
  
  
  .btn-cta-navbar.active {
    transform: scaleX(1.02);
    transition: transform .6s cubic-bezier(.34, 5.56, .64, 1);
  }

  .btn-cta-navbar {
    cursor: pointer;
  }
  
  
  .btn-cta-border-navbar {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 1px;
    z-index: 3;
    border: 1px solid;
    border-radius: 1000px;
  }
  
  .btn-cta-ripple-navbar {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    transform: translateZ(0);
    border-radius: inherit;
  }
  
  .btn-cta-ripple-navbar span {
    display: block;
    width: 100%;
    height: 100%;
    transform: translateY(101%);
    background: currentColor;
    border-radius: 50% 50% 0 0;
    transition: transform .5s cubic-bezier(.4, 0, 0, 1), border-radius .5s cubic-bezier(.4, 0, 0, 1);
    
  }
  
  .btn-cta-navbar.active .btn-cta-ripple-navbar span {
    border-radius: 0;
    transform: translateY(0);
    transition-duration: .5s, .9s;

  }
  
  .btn-cta-title-navbar {
    position: relative;
    display: block;
    padding: 0 .16em 0 0;
    overflow: hidden;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 1.2em; /* Add fixed height to ensure both texts fit */
  }
  
  .btn-cta-title-navbar span {
    display: block;
    transition: transform .8s cubic-bezier(.16, 1, .3, 1);
    
  }
  
  .btn-cta-title-navbar span:after {
    content: attr(data-text);
    display: block;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    color: rgb(177, 55, 55);
      display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  
  .btn-cta-title-navbar span::selection{
    background: var(--secondary-color);
    color: var(--primary-color);
  }
  
  .btn-cta-navbar.active .btn-cta-title-navbar span {
    transform: translateY(-100%);
  }

  .menu-overlay{
    background: rgba(204, 98, 98, 0.5);
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none;
    z-index: 99;
  }

  .menu-overlay.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  .menu-links{
    display: flex;
    flex-direction: column;
    align-items: start;
    justify-content: center;
    background-color: aqua;
    width: 30%;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 101;
    padding: 0rem 2rem;
    transform: translateX(100%);
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .menu-overlay.show .menu-links {
    transform: translateX(0);
  }

  .menu-overlay a{
    display: block;
    padding: 1rem;
    color: rgb(160, 49, 49);
    text-decoration: none;
    font-size: clamp(2.5rem, 3vw, 3rem);
    }
  
</style>
<body>

  <div class="narbar">
    <div class="logo"></div>
    <div class="menu-btn">
           <div class="btn-cta-navbar" href="#">
                <span class="btn-cta-border-navbar"></span>
                <span class="btn-cta-ripple-navbar"><span></span></span>
                <span class="btn-cta-title-navbar"><span data-text="Return">Launch</span></span>
            </div>
    </div>
  </div>
  <div class="menu-overlay">
    <div class="menu-links">

      <a href="#"  data-split="lines">HOME</a>
      <a href="#"  data-split="lines">ABOUT</a>
      <a href="#"  data-split="lines">PROJECTS</a>
      <a href="#"  data-split="lines">CONTACT</a>
    </div>
  </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const menuBtn = document.querySelector('.btn-cta-navbar');
    const menuOverlay = document.querySelector('.menu-overlay');
    const menuLinks = document.querySelector('.menu-links');
    const titleSpan = document.querySelector('.btn-cta-title-navbar span');

    let isMenuOpen = false;

    menuBtn.addEventListener('click', function() {
        if (!isMenuOpen) {
            // Open menu - Launch clicked
            menuBtn.classList.add('active');
            menuOverlay.classList.add('show');
            titleSpan.textContent = 'Return';
            isMenuOpen = true;

            // Prevent body scroll when menu is open
            document.body.style.overflow = 'hidden';
        } else {
            // Close menu - Return clicked
            menuBtn.classList.remove('active');
            menuOverlay.classList.remove('show');
            titleSpan.textContent = 'Launch';
            isMenuOpen = false;

            // Restore body scroll when menu is closed
            document.body.style.overflow = '';
        }
    });

    // Close menu when clicking on overlay (outside menu links)
    menuOverlay.addEventListener('click', function(e) {
        if (e.target === menuOverlay && isMenuOpen) {
            menuBtn.classList.remove('active');
            menuOverlay.classList.remove('show');
            titleSpan.textContent = 'Launch';
            isMenuOpen = false;
            document.body.style.overflow = '';
        }
    });
});
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js" integrity="sha512-NcZdtrT77bJr4STcmsGAESr06BYGE8woZdSdEgqnpyqac7sugNO+Tr4bGwGF3MsnEkGKhU2KL2xh6Ec+BqsaHA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="/js/text-line.js"></script>

</body>
</html>