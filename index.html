<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON> | CG MWT — Codegrid</title>
    <link rel="icon" href="/images/global/site-icon.png" />

    <!-- Performance optimizations: Preload critical resources -->
    <link rel="preload" href="/css/globals.css" as="style" />
    <link rel="preload" href="/css/home.css" as="style" />
    <link rel="preload" href="/css/fonts/NeueMontreal-Regular.otf" as="font" type="font/otf" crossorigin />
    <link rel="preload" href="/images/home/<USER>" as="image" />
    <link rel="preload" href="https://i.gifer.com/2YZ.gif" as="image" />

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net" />
    <link rel="dns-prefetch" href="https://i.gifer.com" />

    <link rel="stylesheet" href="/css/globals.css" />
    <link rel="stylesheet" href="/css/home.css" />
  </head>
  <body>
  

      <div id="main">
            <div class="frame">
      <div class="frame_box">
        <div class="frame_border-top"></div>
        <div class="frame_border-bottom"></div>
        <div class="frame_border-left"></div>
        <div class="frame_border-right"></div>
      </div>
   
   
    </div>



      <!-- home - hero -->
   <section class="hero">
  <h1 class="shader-text"></h1>
  <h1 class="shader-text"></h1>
</section>


      <!-- home - hero img holder -->
<section class="hero-img-holder" >
  <div class="hero-img" style="position: relative; width: 100%; height: 100vh;">
  <div id="shader-container" style="width: 100%; height: 100vh;"></div>

  </div>
</section>




      <section class="story-section"> 

      <div class="story-heading-wrapper">
              <h1  data-split="lines" class="story-heading">OUR STORY</h1>
              <div class="story-heading-gif gif">
                <img src="https://i.gifer.com/2YZ.gif" alt="Story Animation" />
              </div>
              </div>

              <div class="story-content-wrapper">
                <div class="story-text-wrapper">
                  <p data-split="lines">We believe that every brand has a unique story waiting to be told. Our philosophy centers on the power of authentic creativity—where innovation meets purpose, and design transcends aesthetics to create meaningful connections. We don't just build websites or campaigns; we craft digital ecosystems that breathe life into your vision. At Urge Visionary, we embrace the bold, challenge the conventional, and transform ideas into experiences that resonate deeply with your audience. Because in a world full of noise, only the most genuine and visionary voices truly stand out.</p>
                </div>
    </div>
      </section>

         <section class="about-section">

            <div class="about-top-wrapper">
                <h1 data-split="lines">We are a creative agency driven by innovation, design, and strategy. Our mission is to craft digital experiences that captivate, engage, and inspire. Whether it’s branding, web design, or digital marketing, we blend creativity with technology to bring your vision to life. Let’s build something extraordinary together.</h1>
            </div>
         
            <div class="about-bottom-wrapper">
                
                <div class="about-bottom-text">

                <div class="text-icon-wrapper">
                    <h2 data-split="lines">Crafting digital</h2>
                    <div class="switch-lottie" id="switch-lottie"></div>
                </div>
                <h2 data-split="lines">experiences that connect 
                    <h2 data-split="lines">brands with people.
            </div>
            <a class="btn-cta" href="#">
                <span class="btn-cta-border"></span>
                <span class="btn-cta-ripple"><span></span></span>
                <span class="btn-cta-title"><span data-text="READ MORE">READ MORE</span></span>
            </a>
            </div>
         
        </section>


        <section class="work-section">
          <div class="work-heading-wrapper">
              <h1  data-split="lines" class="work-heading">FEATURED WORK</h1>
              <div class="work-heading-gif gif">
                <img src="https://i.gifer.com/2YZ.gif" alt="work Animation" />
              </div>
              </div>

              <div class="projects-wrapper">
                <div class="project-wrapper">
                  <div class="project-dets"><p>DIGITAL MARKETING</p></div>
                  <div class="project-img-wrapper inversion-lens">
                    <img src="/images/home/<USER>" alt="" />
                  </div>
                  <a class="btn-cta" href="#">
                <span class="btn-cta-border"></span>
                <span class="btn-cta-ripple"><span></span></span>
                <span class="btn-cta-title"><span data-text="OH BHAI">OH BHAI</span></span>
            </a>
                </div>
                <div class="project-wrapper">
                  <div class="project-dets"><p>DIGITAL MARKETING</p></div>
                  <div class="project-img-wrapper inversion-lens">
                    <img src="/images/home/<USER>" alt="" />
                  </div>
                  <a class="btn-cta" href="#">
                <span class="btn-cta-border"></span>
                <span class="btn-cta-ripple"><span></span></span>
                <span class="btn-cta-title"><span data-text="OH BHAI">OH BHAI</span></span>
            </a>
                </div>
                <div class="project-wrapper">
                  <div class="project-dets"><p>DIGITAL MARKETING</p></div>
                  <div class="project-img-wrapper inversion-lens">
                    <img src="/images/home/<USER>" alt="" />
                  </div>
                  <a class="btn-cta" href="#">
                <span class="btn-cta-border"></span>
                <span class="btn-cta-ripple"><span></span></span>
                <span class="btn-cta-title"><span data-text="OH BHAI">OH BHAI</span></span>
            </a>
                </div>
                </div>
            
              
            </section>

            <section class="scroll-services">
                 <div class="centered-section">
            <div class="fixed-svg">
              <?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" width="60" height="60"  version="1.1" viewBox="0 0 1080 1080">


  <g>
    <path class="st0" d="M344.1,527.6c0,65.2,32.3,122.8,81.9,157.7-153.3-42.7-252.7-157.7-252.7-157.7h0c0,0,99.4-115.1,252.7-157.8-49.5,34.9-81.9,92.6-81.9,157.7Z"/>
    <path class="st0" d="M900.5,527.5h0c-88.6,88.6-174.4,137-254.2,158.8,50.4-34.8,83.4-92.9,83.4-158.8s-33-124-83.4-158.8c79.8,21.8,165.6,70.2,254.2,158.7Z"/>
  </g>
  <g>
    <path class="st0" d="M549.3,502.6c0,4.5-1.1,8.1-3.2,10.6s-5.2,3.8-9.4,3.8-7.2-1.2-9.3-3.7c-2-2.5-3-6-3-10.7v-38.4h24.9"/>
    <path class="st0" d="M609,484.1v34.5c0,5-1.9,9.9-5.4,13.6l-52.2,55.9c-7.9,8.4-21.2,8.4-29.1,0l-52.2-55.9c-3.4-3.7-5.4-8.5-5.4-13.6v-34.5c0-11,8.9-19.9,19.9-19.9h17.2v38.4c0,7.8,1.5,14.3,4.4,19.7,2.9,5.4,7,9.4,12.1,12.1,5.2,2.7,11.1,4,17.9,4s12.8-1.4,18.2-4.1c5.4-2.7,9.6-6.8,12.7-12.1,3.1-5.4,4.7-11.9,4.7-19.6v-38.4h17.2c11,0,19.9,8.9,19.9,19.9Z"/>
  </g>
</svg>
            </div>
            <div class="text-container">
                <div class="text-row">
                    <span>Digital</span>
                    <span>Innovation</span>
                </div>
                <div class="text-row">
                    <span>Creative</span>
                    <span>Design</span>
                </div>
                <div class="text-row">
                    <span>Brand</span>
                    <span>Strategy</span>
                </div>
                <div class="text-row">
                    <span>Web</span>
                    <span>Development</span>
                </div>
                <div class="text-row">
                    <span>User</span>
                    <span>Experience</span>
                </div>
                <div class="text-row">
                    <span>Visual</span>
                    <span>Identity</span>
                </div>
                <div class="text-row">
                    <span>Marketing</span>
                    <span>Solutions</span>
                </div>
                <div class="text-row">
                    <span>Content</span>
                    <span>Creation</span>
                </div>
                <div class="text-row">
                    <span>Social</span>
                    <span>Media</span>
                </div>
                <div class="text-row">
                    <span>Digital</span>
                    <span>Campaigns</span>
                </div>
                <div class="text-row">
                    <span>Creative</span>
                    <span>Direction</span>
                </div>
                <div class="text-row">
                    <span>Brand</span>
                    <span>Storytelling</span>
                </div>
                <div class="text-row">
                    <span>Interactive</span>
                    <span>Experiences</span>
                </div>
                <div class="text-row">
                    <span>Motion</span>
                    <span>Graphics</span>
                </div>
                <div class="text-row">
                    <span>Digital</span>
                    <span>Excellence</span>
                </div>
                <div class="text-row">
                    <span>Creative</span>
                    <span>Vision</span>
                </div>
                <div class="text-row">
                    <span>Client</span>
                    <span>Success</span>
                </div>
                <div class="text-row">
                    <span>Innovation</span>
                    <span>Driven</span>
                </div>
            </div>
        </div>    

</section>
            
<section class="scroll-steps">
  <div class="canvas-wrapper">
    <canvas id="three-canvas"></canvas>
  </div>

  <div class="scroll-step" id="step-1">
    <div class="text left" style="font-size: clamp(2.2rem, 3vw, 3.2rem);">1. Discover</div>
    <div class="text left">We begin by understanding your brand, goals, and audience. Every great project starts with listening and research.</div>
  </div>
  <div class="scroll-step" id="step-2">
    <div class="text right" style="font-size: clamp(2.2rem, 3vw, 3.2rem);">2. Design</div>
    <div class="text right">Our team crafts visually engaging and strategy-driven designs that reflect your brand's unique identity</div>
  </div>
  <div class="scroll-step" id="step-3">
    <div class="text left" style="font-size: clamp(2.2rem, 3vw, 3.2rem);" style="font-size: clamp(2.2rem, 3vw, 3.2rem);">3. Develop</div>
    <div class="text left">We build fast, responsive, and scalable digital experiences—turning creative concepts into functional solutions</div>
  </div>
  <div class="scroll-step" id="step-4">
    <div class="text right" style="font-size: clamp(2.2rem, 3vw, 3.2rem);">4. Deliver</div>
    <div class="text right">From final launch to long-term support, we ensure smooth delivery with a focus on performance and results</div>
  </div>
</section>

     <section class="footer-section"> 

                <div class="footer-text-wrapper">
                  <p class="text">At Urge Visionary, we don’t just deliver projects — we craft digital experiences that connect, inspire, and convert. Our streamlined process ensures that every idea is nurtured with creativity and executed with precision. Whether you're a startup finding your voice or an established brand looking to scale, we’re here to turn your vision into results. Let’s collaborate and create something extraordinary together.</p>
      <a class="btn-cta-footer" href="#">
                <span class="btn-cta-border-footer"></span>
                <span class="btn-cta-ripple-footer"><span></span></span>
                <span class="btn-cta-title-footer"><span data-text="REACH US">REACH US</span></span>
            </a>
<div class="footer-bottom">
    <div class="footer-logo">
      <h1 class="text">UREG VISIONARY</h1>
    </div>
    <div class="footer-dets">
      <a class="text" href="mailto:<EMAIL>"><EMAIL></a>
      <a class="text" href="tel:+971 58 123 4567">+971 58 123 4567</a>      
    </div>
</div>
    </div>
    
      </section>
      


          </div>

    <!-- Original scripts restored -->
    <script type="module" src="/js/lenis-scroll.js"></script>
    <script type="module" src="/js/home.js"></script>
    <script type="module" src="/js/text-line.js"></script>
    <script type="module" src="/shader/script.js"></script>

<script type="module">
import * as THREE from 'https://cdn.jsdelivr.net/npm/three@0.160.0/build/three.module.js';
import { GUI } from 'https://cdn.jsdelivr.net/npm/lil-gui@0.18.2/+esm';

// 🔧 Speed Control: Change this value to control reveal duration
const config = {
  speed: 0.3, 
  softness: 0.03,
  turbulence: 5.0,
  displacementMix: 0.23,
  paused: false,
  revealComplete: false
};

// Setup
const container = document.getElementById("shader-container");
const renderer = new THREE.WebGLRenderer({ alpha: true });
renderer.setSize(container.clientWidth, container.clientHeight);
container.appendChild(renderer.domElement);

const scene = new THREE.Scene();
const camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
const geometry = new THREE.PlaneGeometry(2, 2);

const textureLoader = new THREE.TextureLoader();
let imageAspect = 1;

const imageTexture = textureLoader.load("public/images/home/<USER>", (img) => {
  imageAspect = img.image.width / img.image.height;
  uniforms.u_imageAspect.value = imageAspect;
});

// Uniforms
const uniforms = {
  iResolution: { value: new THREE.Vector2(container.clientWidth, container.clientHeight) },
  iTime: { value: 0 },
  iChannel1: { value: imageTexture },
  u_speed: { value: config.speed },
  u_softness: { value: config.softness },
  u_turbulence: { value: config.turbulence },
  u_mixAmount: { value: config.displacementMix },
  u_imageAspect: { value: imageAspect }
};

// Shader
const material = new THREE.ShaderMaterial({
  transparent: true,
  uniforms: uniforms,
  fragmentShader: `
    uniform vec2 iResolution;
    uniform float iTime;
    uniform sampler2D iChannel1;
    uniform float u_speed;
    uniform float u_softness;
    uniform float u_turbulence;
    uniform float u_mixAmount;
    uniform float u_imageAspect;

    #define R iResolution.xy

    float random(vec2 st) {
      return fract(sin(dot(st, vec2(94.23, 48.127)) + 14.23) * 1124.23);
    }

    float noise(vec2 st) {
      vec2 ip = floor(st);
      vec2 fp = fract(st);
      float a = random(ip);
      float b = random(ip + vec2(1., 0.));
      float c = random(ip + vec2(0., 1.));
      float d = random(ip + vec2(1., 1.));
      vec2 u = smoothstep(0., 1., fp);
      return mix(mix(a, b, u.x), mix(c, d, u.x), u.y);
    }

    float fractalNoise(vec2 uv) {
      uv *= u_turbulence;
      float amp = 0.6, n = 0.0;
      for (int i = 0; i < 6; i++) {
        n += noise(uv) * amp;
        uv *= 2.0;
        amp *= 0.5;
      }
      return n;
    }

    float displace(vec2 uv) {
      uv = mix(uv, vec2(fractalNoise(uv)), u_mixAmount);
      float d = 1.0 - min(iTime * u_speed, 1.2); // clamp animation
      return smoothstep(d - u_softness, d, uv.y);
    }

    vec3 burn(vec3 col, vec2 uv) {
      float a = displace(uv);
      vec3 b = (1.0 - a) * vec3(1., 0.14, 0.016) * a * 100.0;
      return col * a + b;
    }

    void main() {
  vec2 uv = gl_FragCoord.xy / R;

  float screenAspect = R.x / R.y;
  float imageAspect = u_imageAspect;

  if (screenAspect > imageAspect) {
    float scale = imageAspect / screenAspect;
    uv.y = uv.y * scale + (1.0 - scale) / 2.0;
  } else {
    float scale = screenAspect / imageAspect;
    uv.x = uv.x * scale + (1.0 - scale) / 2.0;
  }

  vec3 color = texture2D(iChannel1, uv).rgb;
  float reveal = displace(uv);

  if (iTime * u_speed >= 1.2) {
    // ✅ Reveal complete — show full image with full alpha
    gl_FragColor = vec4(color, 1.0);
  } else {
    // ✅ Reveal in progress — blend + apply alpha as reveal mask
    vec3 burned = burn(color, uv);
    gl_FragColor = vec4(burned, reveal); // 🟢 Key fix: use reveal as alpha!
  }
}

  `
});

const mesh = new THREE.Mesh(geometry, material);
scene.add(mesh);

// Animate
let clock = new THREE.Clock();
function animate() {
  if (!config.paused && uniforms.iTime.value < (1.2 / config.speed)) {
    uniforms.iTime.value += clock.getDelta();
  }

  uniforms.u_speed.value = config.speed;
  uniforms.u_softness.value = config.softness;
  uniforms.u_turbulence.value = config.turbulence;
  uniforms.u_mixAmount.value = config.displacementMix;
  uniforms.u_imageAspect.value = imageAspect;

  uniforms.iResolution.value.set(container.clientWidth, container.clientHeight);
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.render(scene, camera);
  requestAnimationFrame(animate);
}
animate();

// GUI (optional, hidden)
const gui = new GUI({ title: 'Controls' });
gui.add(config, 'speed', 0.05, 1.0).step(0.01).name("Reveal Speed");
gui.add(config, 'softness', 0.01, 0.3).step(0.01).name("Edge Softness");
gui.add(config, 'turbulence', 5, 80).step(1).name("Turbulence");
gui.add(config, 'displacementMix', 0.0, 0.3).step(0.01).name("Noise Mix");
gui.add(config, 'paused').name("Pause Animation");
gui.hide();
</script>










  </body>
</html>
